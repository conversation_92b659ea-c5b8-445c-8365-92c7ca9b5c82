// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		A1000001 /* MonetaSampleApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000000 /* MonetaSampleApp.swift */; };
		A1000003 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000002 /* ContentView.swift */; };
		A1000005 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A1000004 /* Assets.xcassets */; };
		A1000008 /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A1000007 /* Preview Assets.xcassets */; };
		A1000010 /* MainTabView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A100000F /* MainTabView.swift */; };
		A1000012 /* OnboardingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000011 /* OnboardingView.swift */; };
		A1000014 /* DashboardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000013 /* DashboardView.swift */; };
		A1000016 /* TransactionsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000015 /* TransactionsView.swift */; };
		A1000018 /* RecommendationsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000017 /* RecommendationsView.swift */; };
		A100001A /* PublisherAuthView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000019 /* PublisherAuthView.swift */; };
		A100001C /* SettingsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A100001B /* SettingsView.swift */; };
		A100001E /* OnboardingViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = A100001D /* OnboardingViewModel.swift */; };
		A1000020 /* DashboardViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = A100001F /* DashboardViewModel.swift */; };
		A1000022 /* TransactionsViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000021 /* TransactionsViewModel.swift */; };
		A1000024 /* RecommendationsViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000023 /* RecommendationsViewModel.swift */; };
		A1000026 /* PublisherAuthViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000025 /* PublisherAuthViewModel.swift */; };
		A1000028 /* SettingsViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000027 /* SettingsViewModel.swift */; };
		A100002A /* TransactionRow.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000029 /* TransactionRow.swift */; };
		A100002C /* MonetaSDK in Frameworks */ = {isa = PBXBuildFile; productRef = A100002B /* MonetaSDK */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		A0FFFFFF /* MonetaSampleApp.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = MonetaSampleApp.app; sourceTree = BUILT_PRODUCTS_DIR; };
		A1000000 /* MonetaSampleApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MonetaSampleApp.swift; sourceTree = "<group>"; };
		A1000002 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		A1000004 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		A1000007 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		A100000F /* MainTabView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MainTabView.swift; sourceTree = "<group>"; };
		A1000011 /* OnboardingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OnboardingView.swift; sourceTree = "<group>"; };
		A1000013 /* DashboardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DashboardView.swift; sourceTree = "<group>"; };
		A1000015 /* TransactionsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TransactionsView.swift; sourceTree = "<group>"; };
		A1000017 /* RecommendationsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecommendationsView.swift; sourceTree = "<group>"; };
		A1000019 /* PublisherAuthView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PublisherAuthView.swift; sourceTree = "<group>"; };
		A100001B /* SettingsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingsView.swift; sourceTree = "<group>"; };
		A100001D /* OnboardingViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OnboardingViewModel.swift; sourceTree = "<group>"; };
		A100001F /* DashboardViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DashboardViewModel.swift; sourceTree = "<group>"; };
		A1000021 /* TransactionsViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TransactionsViewModel.swift; sourceTree = "<group>"; };
		A1000023 /* RecommendationsViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecommendationsViewModel.swift; sourceTree = "<group>"; };
		A1000025 /* PublisherAuthViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PublisherAuthViewModel.swift; sourceTree = "<group>"; };
		A1000027 /* SettingsViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingsViewModel.swift; sourceTree = "<group>"; };
		A1000029 /* TransactionRow.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TransactionRow.swift; sourceTree = "<group>"; };
		A100002D /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		A0FFFFFE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A100002C /* MonetaSDK in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		A0FFFFFD /* MonetaSampleApp */ = {
			isa = PBXGroup;
			children = (
				A1000000 /* MonetaSampleApp.swift */,
				A1000002 /* ContentView.swift */,
				A100000E /* Views */,
				A100002E /* ViewModels */,
				A1000004 /* Assets.xcassets */,
				A100002D /* Info.plist */,
				A1000006 /* Preview Content */,
			);
			path = MonetaSampleApp;
			sourceTree = "<group>";
		};
		A1000006 /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				A1000007 /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		A100000E /* Views */ = {
			isa = PBXGroup;
			children = (
				A100000F /* MainTabView.swift */,
				A1000011 /* OnboardingView.swift */,
				A1000013 /* DashboardView.swift */,
				A1000015 /* TransactionsView.swift */,
				A1000017 /* RecommendationsView.swift */,
				A1000019 /* PublisherAuthView.swift */,
				A100001B /* SettingsView.swift */,
				A100002F /* Components */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		A100002E /* ViewModels */ = {
			isa = PBXGroup;
			children = (
				A100001D /* OnboardingViewModel.swift */,
				A100001F /* DashboardViewModel.swift */,
				A1000021 /* TransactionsViewModel.swift */,
				A1000023 /* RecommendationsViewModel.swift */,
				A1000025 /* PublisherAuthViewModel.swift */,
				A1000027 /* SettingsViewModel.swift */,
			);
			path = ViewModels;
			sourceTree = "<group>";
		};
		A100002F /* Components */ = {
			isa = PBXGroup;
			children = (
				A1000029 /* TransactionRow.swift */,
			);
			path = Components;
			sourceTree = "<group>";
		};
		A0FFFFFC = {
			isa = PBXGroup;
			children = (
				A0FFFFFD /* MonetaSampleApp */,
				A0FFFFFF /* Products */,
			);
			sourceTree = "<group>";
		};
		A0FFFFFF /* Products */ = {
			isa = PBXGroup;
			children = (
				A0FFFFFF /* MonetaSampleApp.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		A0FFFFFE /* MonetaSampleApp */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A100000B /* Build configuration list for PBXNativeTarget "MonetaSampleApp" */;
			buildPhases = (
				A0FFFFFB /* Sources */,
				A0FFFFFE /* Frameworks */,
				A0FFFFFD /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = MonetaSampleApp;
			packageProductDependencies = (
				A100002B /* MonetaSDK */,
			);
			productName = MonetaSampleApp;
			productReference = A0FFFFFF /* MonetaSampleApp.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		A0FFFFFA /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1500;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					A0FFFFFE = {
						CreatedOnToolsVersion = 15.0;
					};
				};
			};
			buildConfigurationList = A0FFFFFD /* Build configuration list for PBXProject "MonetaSampleApp" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = A0FFFFFC;
			packageReferences = (
				A100002A /* XCRemoteSwiftPackageReference "MonetaSDK" */,
			);
			productRefGroup = A0FFFFFF /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				A0FFFFFE /* MonetaSampleApp */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		A0FFFFFD /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1000008 /* Preview Assets.xcassets in Resources */,
				A1000005 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		A0FFFFFB /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1000003 /* ContentView.swift in Sources */,
				A1000001 /* MonetaSampleApp.swift in Sources */,
				A1000010 /* MainTabView.swift in Sources */,
				A1000012 /* OnboardingView.swift in Sources */,
				A1000014 /* DashboardView.swift in Sources */,
				A1000016 /* TransactionsView.swift in Sources */,
				A1000018 /* RecommendationsView.swift in Sources */,
				A100001A /* PublisherAuthView.swift in Sources */,
				A100001C /* SettingsView.swift in Sources */,
				A100001E /* OnboardingViewModel.swift in Sources */,
				A1000020 /* DashboardViewModel.swift in Sources */,
				A1000022 /* TransactionsViewModel.swift in Sources */,
				A1000024 /* RecommendationsViewModel.swift in Sources */,
				A1000026 /* PublisherAuthViewModel.swift in Sources */,
				A1000028 /* SettingsViewModel.swift in Sources */,
				A100002A /* TransactionRow.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		******** /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		A100000A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		A100000C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"MonetaSampleApp/Preview Content\"";
				DEVELOPMENT_TEAM = "";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = MonetaSampleApp/Info.plist;
				INFOPLIST_KEY_NSCameraUsageDescription = "This app needs camera access to scan QR codes for publisher authentication.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.moneta.sampleapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		A100000D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"MonetaSampleApp/Preview Content\"";
				DEVELOPMENT_TEAM = "";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = MonetaSampleApp/Info.plist;
				INFOPLIST_KEY_NSCameraUsageDescription = "This app needs camera access to scan QR codes for publisher authentication.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.moneta.sampleapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		A0FFFFFD /* Build configuration list for PBXProject "MonetaSampleApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				******** /* Debug */,
				A100000A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A100000B /* Build configuration list for PBXNativeTarget "MonetaSampleApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A100000C /* Debug */,
				A100000D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		A100002A /* XCRemoteSwiftPackageReference "MonetaSDK" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "../";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.0.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		A100002B /* MonetaSDK */ = {
			isa = XCSwiftPackageProductDependency;
			package = A100002A /* XCRemoteSwiftPackageReference "MonetaSDK" */;
			productName = MonetaSDK;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = A0FFFFFA /* Project object */;
}