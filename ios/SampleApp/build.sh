#!/bin/bash

# Moneta iOS Sample App Build Script
# This script builds and optionally runs the iOS sample app

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="MonetaSampleApp"
SCHEME="MonetaSampleApp"
CONFIGURATION="Debug"
SIMULATOR_NAME="iPhone 15"

# Functions
print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  Moneta iOS Sample App Builder${NC}"
    echo -e "${BLUE}================================${NC}"
    echo
}

print_step() {
    echo -e "${YELLOW}➤ $1${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

check_requirements() {
    print_step "Checking requirements..."
    
    # Check if Xcode is installed
    if ! command -v xcodebuild &> /dev/null; then
        print_error "Xcode is not installed or not in PATH"
        exit 1
    fi
    
    # Check if we're in the right directory
    if [ ! -f "${PROJECT_NAME}.xcodeproj/project.pbxproj" ]; then
        print_error "Not in the correct directory. Please run from ios/SampleApp/"
        exit 1
    fi
    
    print_success "Requirements check passed"
}

clean_build() {
    print_step "Cleaning previous builds..."
    xcodebuild clean \
        -project "${PROJECT_NAME}.xcodeproj" \
        -scheme "${SCHEME}" \
        -configuration "${CONFIGURATION}" \
        > /dev/null 2>&1
    print_success "Clean completed"
}

build_app() {
    print_step "Building ${PROJECT_NAME}..."
    
    xcodebuild build \
        -project "${PROJECT_NAME}.xcodeproj" \
        -scheme "${SCHEME}" \
        -configuration "${CONFIGURATION}" \
        -destination "platform=iOS Simulator,name=${SIMULATOR_NAME}" \
        | xcpretty || exit 1
    
    print_success "Build completed successfully"
}

run_simulator() {
    print_step "Starting iOS Simulator..."
    
    # Boot the simulator if not already running
    xcrun simctl boot "${SIMULATOR_NAME}" 2>/dev/null || true
    
    # Install and run the app
    xcodebuild build \
        -project "${PROJECT_NAME}.xcodeproj" \
        -scheme "${SCHEME}" \
        -configuration "${CONFIGURATION}" \
        -destination "platform=iOS Simulator,name=${SIMULATOR_NAME}" \
        | xcpretty
    
    print_success "App launched in simulator"
}

show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo
    echo "Options:"
    echo "  -c, --clean     Clean before building"
    echo "  -r, --run       Build and run in simulator"
    echo "  -s, --simulator SIMULATOR_NAME  Specify simulator (default: iPhone 15)"
    echo "  -h, --help      Show this help message"
    echo
    echo "Examples:"
    echo "  $0                    # Just build"
    echo "  $0 --clean --run     # Clean, build, and run"
    echo "  $0 -s 'iPad Pro'     # Build for iPad Pro simulator"
}

# Parse command line arguments
CLEAN=false
RUN=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -c|--clean)
            CLEAN=true
            shift
            ;;
        -r|--run)
            RUN=true
            shift
            ;;
        -s|--simulator)
            SIMULATOR_NAME="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Main execution
print_header

check_requirements

if [ "$CLEAN" = true ]; then
    clean_build
fi

build_app

if [ "$RUN" = true ]; then
    run_simulator
fi

echo
print_success "All tasks completed successfully!"
echo -e "${BLUE}You can now open the app in Xcode or run it in the simulator.${NC}"
