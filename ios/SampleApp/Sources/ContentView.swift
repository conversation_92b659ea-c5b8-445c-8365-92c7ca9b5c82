import SwiftUI

struct ContentView: View {
    @StateObject private var appState = AppState()
    
    var body: some View {
        Group {
            if appState.isOnboardingCompleted {
                MainTabView()
            } else {
                OnboardingView()
            }
        }
        .environmentObject(appState)
        .onAppear {
            appState.checkOnboardingStatus()
        }
    }
}

// MARK: - App State Management
@MainActor
class AppState: ObservableObject {
    @Published var isOnboardingCompleted: Bool = false
    @Published var isLoading: Bool = true
    
    func checkOnboardingStatus() {
        // In a real app, you would check if the user has completed onboarding
        // For demo purposes, we'll assume they need to onboard first
        Task {
            try? await Task.sleep(nanoseconds: 1_000_000_000) // 1 second delay
            isLoading = false
            // Check if user data exists in secure storage
            // For now, we'll default to false to show onboarding
            isOnboardingCompleted = false
        }
    }
    
    func completeOnboarding() {
        isOnboardingCompleted = true
    }
}

#Preview {
    ContentView()
}
