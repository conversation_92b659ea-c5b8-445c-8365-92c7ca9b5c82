import Foundation
import MonetaSDK

@MainActor
class PublisherAuthViewModel: ObservableObject {
    @Published var isLoading = false
    @Published var authResult: OnboardUserResponse?
    @Published var errorMessage: String?
    
    func authenticatePublisher(qrCodeContent: String) async {
        isLoading = true
        errorMessage = nil
        
        do {
            // Parse QR code content
            // In a real app, this would be more sophisticated parsing
            let (qrCodeId, session) = parseQRCode(qrCodeContent)
            
            let request = PublisherLoginRequest(session: session)
            let response = try await MonetaSDK.shared.membershipOrgClient.publisherLogin(
                qrCodeId: qrCodeId,
                request: request
            )
            
            if let userData = response.data {
                authResult = userData
            } else {
                errorMessage = "Authentication failed: No user data received"
            }
            
        } catch {
            errorMessage = "Authentication failed: \(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    private func parseQRCode(_ content: String) -> (qrCodeId: String, session: String) {
        // Try to parse the QR code content
        // Expected format: "qrCodeId:session" or just the qrCodeId
        let parts = content.split(separator: ":")
        
        if parts.count >= 2 {
            return (String(parts[0]), String(parts[1]))
        } else {
            // If no session in QR code, generate a demo session
            return (content, "demo_session_\(UUID().uuidString.prefix(8))")
        }
    }
    
    func clearAuthResult() {
        authResult = nil
    }
    
    func clearError() {
        errorMessage = nil
    }
}
