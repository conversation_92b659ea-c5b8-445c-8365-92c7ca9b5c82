import Foundation
import MonetaSDK

@MainActor
class SettingsViewModel: ObservableObject {
    @Published var userProfile: OnboardUserResponse?
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var showingLogoutAlert = false
    
    func loadUserProfile() async {
        isLoading = true
        errorMessage = nil
        
        do {
            // In a real app, you would have an API to get current user profile
            // For demo purposes, we'll create a sample user profile
            userProfile = OnboardUserResponse(
                id: "user_123",
                name: "<PERSON>",
                email: "<EMAIL>",
                status: "active"
            )
            
        } catch {
            errorMessage = "Failed to load user profile: \(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    func signOut() {
        // In a real app, you would:
        // 1. Clear stored authentication tokens
        // 2. Clear secure storage
        // 3. Reset app state
        // 4. Navigate back to onboarding
        
        // For demo purposes, we'll just show an alert
        // In a real implementation, you would need to communicate with the main app state
        print("User signed out")
    }
    
    func clearError() {
        errorMessage = nil
    }
}
