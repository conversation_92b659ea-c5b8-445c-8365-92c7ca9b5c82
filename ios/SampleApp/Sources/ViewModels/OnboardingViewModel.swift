import Foundation
import MonetaSDK

@MainActor
class OnboardingViewModel: ObservableObject {
    @Published var isLoading = false
    @Published var onboardingData: OnboardVerificationResponse?
    @Published var isOnboardingCompleted = false
    @Published var errorMessage: String?
    
    func startOnboarding() async {
        isLoading = true
        errorMessage = nil
        
        do {
            let data = try await MonetaSDK.shared.startOnboarding()
            onboardingData = data
        } catch {
            errorMessage = "Failed to start onboarding: \(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    func completeOnboarding() async {
        isLoading = true
        errorMessage = nil
        
        do {
            _ = try await MonetaSDK.shared.completeOnboarding()
            isOnboardingCompleted = true
        } catch {
            errorMessage = "Failed to complete onboarding: \(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    func clearError() {
        errorMessage = nil
    }
}
