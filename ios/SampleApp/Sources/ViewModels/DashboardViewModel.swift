import Foundation
import MonetaSDK

@MainActor
class DashboardViewModel: ObservableObject {
    @Published var isLoading = false
    @Published var balance: UserBalanceResponse?
    @Published var recentTransactions: [TransactionResponse] = []
    @Published var errorMessage: String?
    
    func loadDashboardData() async {
        isLoading = true
        errorMessage = nil
        
        do {
            // Load balance and recent transactions concurrently
            async let balanceTask = loadBalance()
            async let transactionsTask = loadRecentTransactions()
            
            let (balanceResult, transactionsResult) = await (balanceTask, transactionsTask)
            
            if let balance = balanceResult {
                self.balance = balance
            }
            
            if let transactions = transactionsResult {
                self.recentTransactions = Array(transactions.prefix(5)) // Show only 5 recent
            }
            
        } catch {
            errorMessage = "Failed to load dashboard data: \(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    private func loadBalance() async -> UserBalanceResponse? {
        do {
            let response = try await MonetaSDK.shared.membershipOrgClient.getUserBalance()
            return response.data
        } catch {
            print("Failed to load balance: \(error)")
            return nil
        }
    }
    
    private func loadRecentTransactions() async -> [TransactionResponse]? {
        do {
            // Get current month in YYYY-MM format
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM"
            let currentMonth = formatter.string(from: Date())
            
            let response = try await MonetaSDK.shared.membershipOrgClient.fetchTransactions(
                month: currentMonth,
                pageSize: 10
            )
            return response.data?.content
        } catch {
            print("Failed to load transactions: \(error)")
            return nil
        }
    }
    
    func clearError() {
        errorMessage = nil
    }
}
