import SwiftUI
import MonetaSD<PERSON>

struct TransactionRow: View {
    let transaction: TransactionResponse
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(transaction.publisherName ?? "Unknown Publisher")
                        .font(.headline)
                        .lineLimit(1)
                    
                    Text(formatDate(transaction.transactionDate))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text(formatCurrency(transaction.amount, currency: transaction.currency))
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(transaction.amount >= 0 ? .green : .red)
                    
                    StatusBadge(status: transaction.status)
                }
            }
            
            if let description = transaction.description, !description.isEmpty {
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
        }
        .padding(16)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
    
    private func formatCurrency(_ amount: Double, currency: String) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = currency
        return formatter.string(from: NSNumber(value: amount)) ?? "$\(amount)"
    }
    
    private func formatDate(_ dateString: String) -> String {
        let inputFormatter = DateFormatter()
        inputFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'"
        inputFormatter.timeZone = TimeZone(abbreviation: "UTC")
        
        let outputFormatter = DateFormatter()
        outputFormatter.dateStyle = .medium
        outputFormatter.timeStyle = .short
        
        if let date = inputFormatter.date(from: dateString) {
            return outputFormatter.string(from: date)
        } else {
            return dateString
        }
    }
}

struct StatusBadge: View {
    let status: String
    
    var body: some View {
        Text(status.capitalized)
            .font(.caption2)
            .fontWeight(.medium)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(backgroundColor)
            .foregroundColor(textColor)
            .cornerRadius(8)
    }
    
    private var backgroundColor: Color {
        switch status.lowercased() {
        case "completed", "success":
            return .green.opacity(0.2)
        case "pending":
            return .orange.opacity(0.2)
        case "failed", "error":
            return .red.opacity(0.2)
        default:
            return .gray.opacity(0.2)
        }
    }
    
    private var textColor: Color {
        switch status.lowercased() {
        case "completed", "success":
            return .green
        case "pending":
            return .orange
        case "failed", "error":
            return .red
        default:
            return .gray
        }
    }
}

#Preview {
    VStack {
        TransactionRow(transaction: TransactionResponse(
            id: "1",
            amount: 25.99,
            currency: "USD",
            description: "Coffee purchase at Starbucks",
            transactionDate: "2024-01-15T10:30:00.000000Z",
            status: "completed",
            publisherName: "Starbucks",
            publisherId: "pub_123"
        ))
        
        TransactionRow(transaction: TransactionResponse(
            id: "2",
            amount: -15.50,
            currency: "USD",
            description: "Refund for cancelled order",
            transactionDate: "2024-01-14T14:20:00.000000Z",
            status: "pending",
            publisherName: "Amazon",
            publisherId: "pub_456"
        ))
    }
    .padding()
}
