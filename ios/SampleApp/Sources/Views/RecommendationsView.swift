import SwiftUI
import MonetaSDK

struct RecommendationsView: View {
    @StateObject private var viewModel = RecommendationsViewModel()
    
    var body: some View {
        NavigationView {
            Group {
                if viewModel.isLoading {
                    ProgressView("Loading recommendations...")
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else if viewModel.recommendations.isEmpty {
                    EmptyRecommendationsView()
                } else {
                    ScrollView {
                        LazyVStack(spacing: 16) {
                            ForEach(viewModel.recommendations, id: \.id) { recommendation in
                                RecommendationCard(recommendation: recommendation)
                            }
                        }
                        .padding(.horizontal, 16)
                        .padding(.top, 8)
                    }
                    .refreshable {
                        await viewModel.loadRecommendations()
                    }
                }
            }
            .navigationTitle("Recommendations")
        }
        .alert("Error", isPresented: .constant(viewModel.errorMessage != nil)) {
            Button("Retry") {
                Task {
                    await viewModel.loadRecommendations()
                }
            }
            But<PERSON>("OK") {
                viewModel.clearError()
            }
        } message: {
            if let error = viewModel.errorMessage {
                Text(error)
            }
        }
        .task {
            await viewModel.loadRecommendations()
        }
    }
}

struct RecommendationCard: View {
    let recommendation: UARecommendation
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(recommendation.title ?? "Recommendation")
                        .font(.headline)
                        .lineLimit(2)
                    
                    if let source = recommendation.source {
                        Text("Source: \(source)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                if let score = recommendation.score {
                    VStack {
                        Text("\(Int(score * 100))%")
                            .font(.caption)
                            .fontWeight(.semibold)
                        Text("Match")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(8)
                }
            }
            
            if let description = recommendation.description {
                Text(description)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .lineLimit(3)
            }
            
            if let url = recommendation.url {
                Button(action: {
                    if let url = URL(string: url) {
                        UIApplication.shared.open(url)
                    }
                }) {
                    Text("Learn More")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.blue)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }
            }
        }
        .padding(16)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
}

struct EmptyRecommendationsView: View {
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "star")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            Text("No Recommendations")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("Personalized recommendations will appear here based on your activity")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 32)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

#Preview {
    RecommendationsView()
}
