import SwiftUI
import MonetaSD<PERSON>

struct TransactionsView: View {
    @StateObject private var viewModel = TransactionsViewModel()
    
    var body: some View {
        NavigationView {
            Group {
                if viewModel.isLoading && viewModel.transactions.isEmpty {
                    ProgressView("Loading transactions...")
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else if viewModel.transactions.isEmpty {
                    EmptyTransactionsView()
                } else {
                    ScrollView {
                        LazyVStack(spacing: 12) {
                            ForEach(viewModel.transactions, id: \.id) { transaction in
                                TransactionRow(transaction: transaction)
                            }
                            
                            if viewModel.isLoadingMore {
                                ProgressView()
                                    .padding()
                            }
                        }
                        .padding(.horizontal, 16)
                        .padding(.top, 8)
                    }
                    .refreshable {
                        await viewModel.loadTransactions(refresh: true)
                    }
                    .onAppear {
                        if viewModel.transactions.isEmpty {
                            Task {
                                await viewModel.loadTransactions()
                            }
                        }
                    }
                    .onScrollToBottom {
                        if viewModel.hasMorePages && !viewModel.isLoadingMore {
                            Task {
                                await viewModel.loadMoreTransactions()
                            }
                        }
                    }
                }
            }
            .navigationTitle("Transactions")
        }
        .alert("Error", isPresented: .constant(viewModel.errorMessage != nil)) {
            Button("Retry") {
                Task {
                    await viewModel.loadTransactions(refresh: true)
                }
            }
            Button("OK") {
                viewModel.clearError()
            }
        } message: {
            if let error = viewModel.errorMessage {
                Text(error)
            }
        }
    }
}

struct EmptyTransactionsView: View {
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "creditcard")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            Text("No Transactions")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("Your transactions will appear here once you start using the app")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 32)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

// Extension to detect scroll to bottom
extension View {
    func onScrollToBottom(perform action: @escaping () -> Void) -> some View {
        self.background(
            GeometryReader { geometry in
                Color.clear
                    .preference(
                        key: ScrollOffsetPreferenceKey.self,
                        value: geometry.frame(in: .named("scroll")).minY
                    )
            }
        )
        .onPreferenceChange(ScrollOffsetPreferenceKey.self) { value in
            // Trigger action when near bottom
            if value < 100 {
                action()
            }
        }
    }
}

struct ScrollOffsetPreferenceKey: PreferenceKey {
    static var defaultValue: CGFloat = 0
    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        value = nextValue()
    }
}

#Preview {
    TransactionsView()
}
