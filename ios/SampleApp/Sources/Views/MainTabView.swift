import SwiftUI

struct MainTabView: View {
    var body: some View {
        TabView {
            DashboardView()
                .tabItem {
                    Image(systemName: "chart.pie.fill")
                    Text("Dashboard")
                }
                .tag(0)
            
            TransactionsView()
                .tabItem {
                    Image(systemName: "list.bullet.rectangle.fill")
                    Text("Transactions")
                }
                .tag(1)
            
            RecommendationsView()
                .tabItem {
                    Image(systemName: "star.fill")
                    Text("Recommendations")
                }
                .tag(2)
            
            PublisherAuthView()
                .tabItem {
                    Image(systemName: "qrcode")
                    Text("Publisher Auth")
                }
                .tag(3)
            
            SettingsView()
                .tabItem {
                    Image(systemName: "gearshape.fill")
                    Text("Settings")
                }
                .tag(4)
        }
        .accentColor(.blue)
    }
}

#Preview {
    MainTabView()
}
