import SwiftUI
import MonetaSDK

struct SettingsView: View {
    @StateObject private var viewModel = SettingsViewModel()
    
    var body: some View {
        NavigationView {
            List {
                // User Profile Section
                Section("User Profile") {
                    if let user = viewModel.userProfile {
                        UserProfileRow(user: user)
                    } else {
                        HStack {
                            ProgressView()
                                .scaleEffect(0.8)
                            Text("Loading profile...")
                                .foregroundColor(.secondary)
                        }
                    }
                }
                
                // Content Preferences Section
                Section("Content Preferences") {
                    NavigationLink(destination: ContentPreferencesView()) {
                        Label("Interests", systemImage: "heart")
                    }
                    
                    NavigationLink(destination: NotificationSettingsView()) {
                        Label("Notifications", systemImage: "bell")
                    }
                }
                
                // Account Section
                Section("Account") {
                    NavigationLink(destination: PolicySettingsView()) {
                        Label("Privacy Policies", systemImage: "shield")
                    }
                    
                    Button(action: {
                        viewModel.showingLogoutAlert = true
                    }) {
                        Label("Sign Out", systemImage: "rectangle.portrait.and.arrow.right")
                            .foregroundColor(.red)
                    }
                }
                
                // About Section
                Section("About") {
                    HStack {
                        Text("Version")
                        Spacer()
                        Text("1.0.0")
                            .foregroundColor(.secondary)
                    }
                    
                    Link(destination: URL(string: "https://moneta.com/privacy")!) {
                        Label("Privacy Policy", systemImage: "doc.text")
                    }
                    
                    Link(destination: URL(string: "https://moneta.com/terms")!) {
                        Label("Terms of Service", systemImage: "doc.text")
                    }
                }
            }
            .navigationTitle("Settings")
            .refreshable {
                await viewModel.loadUserProfile()
            }
        }
        .alert("Sign Out", isPresented: $viewModel.showingLogoutAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Sign Out", role: .destructive) {
                viewModel.signOut()
            }
        } message: {
            Text("Are you sure you want to sign out?")
        }
        .alert("Error", isPresented: .constant(viewModel.errorMessage != nil)) {
            Button("OK") {
                viewModel.clearError()
            }
        } message: {
            if let error = viewModel.errorMessage {
                Text(error)
            }
        }
        .task {
            await viewModel.loadUserProfile()
        }
    }
}

struct UserProfileRow: View {
    let user: OnboardUserResponse
    
    var body: some View {
        HStack {
            // Avatar placeholder
            Circle()
                .fill(Color.blue.opacity(0.2))
                .frame(width: 50, height: 50)
                .overlay(
                    Text(user.name.prefix(1).uppercased())
                        .font(.title2)
                        .fontWeight(.semibold)
                        .foregroundColor(.blue)
                )
            
            VStack(alignment: .leading, spacing: 4) {
                Text(user.name)
                    .font(.headline)
                
                Text(user.email)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding(.vertical, 4)
    }
}

// Placeholder views for navigation destinations
struct ContentPreferencesView: View {
    var body: some View {
        List {
            Text("Content preferences will be implemented here")
                .foregroundColor(.secondary)
        }
        .navigationTitle("Interests")
        .navigationBarTitleDisplayMode(.inline)
    }
}

struct NotificationSettingsView: View {
    var body: some View {
        List {
            Text("Notification settings will be implemented here")
                .foregroundColor(.secondary)
        }
        .navigationTitle("Notifications")
        .navigationBarTitleDisplayMode(.inline)
    }
}

struct PolicySettingsView: View {
    var body: some View {
        List {
            Text("Privacy policy settings will be implemented here")
                .foregroundColor(.secondary)
        }
        .navigationTitle("Privacy Policies")
        .navigationBarTitleDisplayMode(.inline)
    }
}

#Preview {
    SettingsView()
}
