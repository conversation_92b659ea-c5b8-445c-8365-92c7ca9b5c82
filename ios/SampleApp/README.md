# Moneta iOS Sample App

A comprehensive sample application demonstrating the integration and usage of the Moneta iOS SDK. This app showcases modern iOS development practices using SwiftUI, following Apple's Human Interface Guidelines.

## Features

### 🚀 **Core Functionality**
- **User Onboarding**: Complete SDK initialization and user registration flow
- **Dashboard**: Real-time balance display and recent transactions overview
- **Transactions**: Comprehensive transaction history with pagination
- **Recommendations**: Personalized content recommendations
- **Publisher Authentication**: QR code scanning for publisher login
- **Settings**: User profile management and app configuration

### 🎨 **Modern iOS Design**
- **SwiftUI**: 100% SwiftUI implementation with declarative UI
- **Universal App**: Optimized for both iPhone and iPad
- **Apple Design Guidelines**: Follows HIG for consistent user experience
- **Adaptive Layout**: Responsive design that works across all device sizes
- **Dark Mode Ready**: Supports both light and dark appearance modes

### 🏗️ **Architecture**
- **MVVM Pattern**: Clean separation of concerns with ViewModels
- **Async/Await**: Modern concurrency for network operations
- **Combine Framework**: Reactive programming for state management
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Loading States**: Proper loading indicators and empty states

## Requirements

- iOS 16.0+
- Xcode 15.0+
- Swift 5.9+

## Installation

### Using Xcode

1. Open `MonetaSampleApp.xcodeproj` in Xcode
2. Select your development team in the project settings
3. Build and run the project

### Using Swift Package Manager

```bash
cd ios/SampleApp
swift build
```

## Project Structure

```
Sources/
├── MonetaSampleApp.swift          # Main app entry point
├── ContentView.swift              # Root view with navigation logic
├── Views/
│   ├── MainTabView.swift          # Tab-based navigation
│   ├── OnboardingView.swift       # User onboarding flow
│   ├── DashboardView.swift        # Main dashboard
│   ├── TransactionsView.swift     # Transaction history
│   ├── RecommendationsView.swift  # Content recommendations
│   ├── PublisherAuthView.swift    # QR code authentication
│   ├── SettingsView.swift         # App settings
│   └── Components/
│       └── TransactionRow.swift   # Reusable transaction component
├── ViewModels/
│   ├── OnboardingViewModel.swift
│   ├── DashboardViewModel.swift
│   ├── TransactionsViewModel.swift
│   ├── RecommendationsViewModel.swift
│   ├── PublisherAuthViewModel.swift
│   └── SettingsViewModel.swift
└── Assets.xcassets/               # App icons and colors
```

## Key Features Explained

### Onboarding Flow
The app starts with a comprehensive onboarding process that:
- Initializes the Moneta SDK
- Generates device credentials
- Displays user codes for web portal completion
- Handles the complete authentication flow

### Dashboard
The main dashboard provides:
- Real-time balance display with currency formatting
- Recent transactions overview
- Pull-to-refresh functionality
- Error handling with retry options

### Transactions
The transactions view features:
- Infinite scrolling with pagination
- Transaction status indicators
- Date and amount formatting
- Empty state handling

### QR Code Scanner
The publisher authentication includes:
- Native camera integration
- QR code scanning capabilities
- Permission handling
- Authentication result display

### Settings
Comprehensive settings management:
- User profile display
- Content preferences
- Privacy policy access
- Sign out functionality

## Usage

### Basic Integration

```swift
import MonetaSDK

// Initialize the SDK
MonetaSDK.shared.initialize(baseUrl: "https://api.moneta.com")

// Start onboarding
let onboardingData = try await MonetaSDK.shared.startOnboarding()

// Complete onboarding
let userData = try await MonetaSDK.shared.completeOnboarding()

// Fetch user balance
let balance = try await MonetaSDK.shared.membershipOrgClient.getUserBalance()

// Get transactions
let transactions = try await MonetaSDK.shared.membershipOrgClient.fetchTransactions(
    month: "2024-01",
    pageSize: 20
)
```

### Error Handling

The app implements comprehensive error handling:

```swift
do {
    let data = try await MonetaSDK.shared.startOnboarding()
    // Handle success
} catch {
    // Display user-friendly error message
    errorMessage = "Failed to start onboarding: \(error.localizedDescription)"
}
```

## Customization

### Theming
The app uses SwiftUI's built-in theming system. You can customize:
- Accent colors in `Assets.xcassets/AccentColor.colorset`
- App icons in `Assets.xcassets/AppIcon.appiconset`
- Typography and spacing throughout the views

### Configuration
Update the SDK base URL in `MonetaSampleApp.swift`:

```swift
MonetaSDK.shared.initialize(baseUrl: "YOUR_API_BASE_URL")
```

## Testing

The app includes comprehensive error handling and loading states that can be tested by:
- Simulating network failures
- Testing with invalid credentials
- Verifying QR code scanning functionality

## Contributing

When contributing to the sample app:
1. Follow SwiftUI best practices
2. Maintain MVVM architecture
3. Add proper error handling
4. Include loading states
5. Test on both iPhone and iPad
6. Ensure accessibility compliance

## License

This sample app is provided as-is for demonstration purposes. See the main project license for details.
