// swift-tools-version: 5.9
import PackageDescription

let package = Package(
    name: "MonetaSampleApp",
    platforms: [
        .iOS(.v16),
        .macOS(.v13)
    ],
    products: [
        .executable(
            name: "MonetaSampleApp",
            targets: ["MonetaSampleApp"]
        )
    ],
    dependencies: [
        .package(path: "../")
    ],
    targets: [
        .executableTarget(
            name: "MonetaSampleApp",
            dependencies: [
                .product(name: "MonetaSDK", package: "MonetaSDK")
            ],
            path: "Sources"
        )
    ]
)
